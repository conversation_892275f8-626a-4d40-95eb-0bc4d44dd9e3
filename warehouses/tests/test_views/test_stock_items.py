from decimal import Decimal

from django.urls import reverse
from rest_framework import status
from rest_framework.exceptions import ErrorDetail

from utils.test.base_test import BaseTestCase
from utils.test.factories.product.product import ProductFactory
from utils.test.factories.warehouse.stock_item import StockItemFactory
from utils.test.factories.warehouse.warehouse import WarehouseFactory


class StockItemViewSetTestCase(BaseTestCase):
    """Test cases for StockItemViewSet"""

    def setUp(self):
        super().setUp()

        # Create test data
        self.warehouse1 = WarehouseFactory()
        self.warehouse2 = WarehouseFactory()
        self.product1 = ProductFactory()
        self.product2 = ProductFactory()

        # Create stock items
        self.stock_item1 = StockItemFactory(
            warehouse=self.warehouse1,
            product=self.product1,
            quantity=Decimal("50.000"),
            min_stock=Decimal("10.000"),
        )
        self.stock_item2 = StockItemFactory(
            warehouse=self.warehouse2,
            product=self.product2,
            quantity=Decimal("100.000"),
            min_stock=Decimal("20.000"),
        )

        # Set up URLs
        self.list_url = reverse(
            "warehouses:warehouse-stock-items-list",
            kwargs={"warehouse_id": self.warehouse1.id},
        )
        self.detail_url = reverse(
            "warehouses:warehouse-stock-items-detail",
            kwargs={"warehouse_id": self.warehouse1.id, "pk": self.stock_item1.id},
        )

    def test_list_stock_items_unauthenticated(self):
        """Test that unauthenticated users cannot list stock items"""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_stock_items_unauthorized(self):
        """Test that non-admin users cannot list stock items"""
        # Test manager cannot list
        response = self.manager_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test cashier cannot list
        response = self.cashier_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_stock_items_admin(self):
        """Test that admin users can list stock items"""
        response = self.admin_client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)  # Only items from warehouse1
        self.assertEqual(response.data["results"][0]["id"], self.stock_item1.id)

    def test_retrieve_stock_item(self):
        """Test retrieving a single stock item"""
        response = self.admin_client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.stock_item1.id)
        self.assertEqual(Decimal(response.data["quantity"]), self.stock_item1.quantity)

    def test_retrieve_stock_items_unauthenticated(self):
        """Test that unauthenticated users cannot list stock items"""
        response = self.client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_retrieve_stock_items_unauthorized(self):
        """Test that non-admin users cannot list stock items"""
        # Test manager cannot list
        response = self.manager_client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test cashier cannot list
        response = self.cashier_client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_filter_by_product(self):
        """Test filtering stock items by product"""
        url = f"{self.list_url}?product={self.product1.id}"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.stock_item1.id)

    def test_search_by_product_name(self):
        """Test searching stock items by product name"""
        url = f"{self.list_url}?search={self.product1.name[:5]}"  # First 5 chars of product name
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data["results"]), 1)
        self.assertEqual(
            response.data["results"][0]["product_name"], self.product1.name
        )

    def test_ordering_by_quantity(self):
        """Test ordering stock items by quantity"""
        # Create another stock item with lower quantity
        stock_item3 = StockItemFactory(
            warehouse=self.warehouse1,
            product=ProductFactory(),
            quantity=Decimal("10.000"),
            min_stock=Decimal("5.000"),
        )

        # Test ascending order
        url = f"{self.list_url}?ordering=quantity"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        self.assertGreaterEqual(len(results), 2)
        self.assertEqual(Decimal(results[0]["quantity"]), stock_item3.quantity)

        # Test descending order
        url = f"{self.list_url}?ordering=-quantity"
        response = self.admin_client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        self.assertEqual(Decimal(results[0]["quantity"]), self.stock_item1.quantity)

    def test_create_stock_item(self):
        """Test creating a new stock item"""
        data = {"product": self.product2.id, "quantity": "30.000", "min_stock": "5.000"}
        response = self.admin_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Decimal(response.data["quantity"]), Decimal("30.000"))
        self.assertEqual(Decimal(response.data["min_stock"]), Decimal("5.000"))
        self.assertEqual(response.data["warehouse"], self.warehouse1.id)

    def test_create_stock_item_unauthorized(self):
        """Test creating a new stock item"""
        data = {"product": self.product2.id, "quantity": "30.000", "min_stock": "5.000"}
        response = self.manager_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        response = self.cashier_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_stock_item_unauthenticated(self):
        """Test creating a new stock item"""
        data = {"product": self.product2.id, "quantity": "30.000", "min_stock": "5.000"}
        response = self.client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_stock_item_duplicate(self):
        """Test creating a duplicate stock item (same warehouse and product)"""
        data = {
            "product": self.stock_item1.product.id,
            "quantity": "30.000",
            "min_stock": "5.000",
        }
        response = self.admin_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            [
                ErrorDetail(
                    string="The fields warehouse, product must make a unique set.",
                    code="unique",
                )
            ],
            response.data["non_field_errors"],
        )

    def test_update_stock_item(self):
        """Test updating a stock item"""
        data = {"quantity": "75.000", "min_stock": "15.000"}
        response = self.admin_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(Decimal(response.data["quantity"]), Decimal("75.000"))
        self.assertEqual(Decimal(response.data["min_stock"]), Decimal("15.000"))

    def test_update_stock_item_unauthorized(self):
        """Test updating a stock item"""
        data = {"quantity": "75.000", "min_stock": "15.000"}
        response = self.manager_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        response = self.cashier_client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_stock_item_unauthenticated(self):
        """Test updating a stock item"""
        data = {"quantity": "75.000", "min_stock": "15.000"}
        response = self.client.patch(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_delete_stock_item(self):
        """Test deleting a stock item"""
        response = self.admin_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Verify the stock item was deleted
        response = self.admin_client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_stock_item_unauthorized(self):
        """Test deleting a stock item"""
        response = self.manager_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        response = self.cashier_client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_stock_item_unauthenticated(self):
        """Test deleting a stock item"""
        response = self.client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_negative_quantity_validation(self):
        """Test validation for negative quantity"""
        data = {
            "product": self.product2.id,
            "quantity": "-10.000",
            "min_stock": "5.000",
        }
        response = self.admin_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            [ErrorDetail(string="Quantity cannot be negative.", code="invalid")],
            response.data["quantity"],
        )

    def test_negative_min_stock_validation(self):
        """Test validation for negative min_stock"""
        data = {
            "product": self.product2.id,
            "quantity": "50.000",
            "min_stock": "-5.000",
        }
        response = self.admin_client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            [ErrorDetail(string="Minimum stock cannot be negative.", code="invalid")],
            response.data["min_stock"],
        )
