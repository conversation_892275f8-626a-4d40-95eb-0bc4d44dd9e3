from django_filters.rest_framework import Django<PERSON>ilter<PERSON>ackend
from rest_framework import mixins, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from utils.pagination import PaginationClass
from utils.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsAdminOrManager

from ..models import InventoryCount
from ..serializers import InventoryCountSerializer


class InventoryCountViewSet(
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    mixins.DestroyModelMixin,
    mixins.ListModelMixin,
    GenericViewSet,
):
    """
    ViewSet for managing inventory counts.
    """

    queryset = InventoryCount.objects.select_related("warehouse", "noted_by")
    serializer_class = InventoryCountSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["status", "warehouse"]
    pagination_class = PaginationClass

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.is_authenticated and self.request.user.is_manager:
            queryset = queryset.filter(
                status__in=[
                    InventoryCount.Status.DRAFT,
                    InventoryCount.Status.IN_PROGRESS,
                ],
                warehouse__pos_employee__user=self.request.user,
            )
        return queryset

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ["list", "retrieve"]:
            permission_classes = [IsAuthenticated, IsAdminOrManager]
        else:
            permission_classes = [IsAuthenticated, IsAdminOnly]
        return [permission() for permission in permission_classes]

    @action(detail=True, methods=["post"])
    def cancel(self, request, pk=None):
        """
        Cancel an inventory count that is in progress.
        """
        inventory_count = self.get_object()
        if inventory_count.status not in [
            InventoryCount.Status.IN_PROGRESS,
            InventoryCount.Status.DRAFT,
        ]:
            return Response(
                "Only in-progress or draft inventory counts can be cancelled",
                status=status.HTTP_400_BAD_REQUEST,
            )
        inventory_count.cancel()
        return Response(
            {"status": "Inventory count cancelled"}, status=status.HTTP_200_OK
        )

    def destroy(self, request, *args, **kwargs):
        """
        Only allow deletion of draft inventory counts.
        """
        instance = self.get_object()
        if instance.status != InventoryCount.Status.DRAFT:
            return Response(
                {"error": "Only draft inventory counts can be deleted"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return super().destroy(request, *args, **kwargs)
