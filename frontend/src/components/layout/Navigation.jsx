import React from 'react';
import {Container, Nav, Navbar, NavDropdown} from 'react-bootstrap';
import {Link, useNavigate} from 'react-router-dom';
import {useAuth} from '../../contexts/AuthContext';

const Navigation = () => {
    const {currentUser, logout, permittedAdmin} = useAuth();
    const navigate = useNavigate();

    const handleLogout = async () => {
        try {
            await logout();
            navigate('/login');
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };

    return (
        <Navbar bg="primary" variant="dark" expand="lg" className="mb-4">
            <Container>
                <Navbar.Brand as={Link} to={currentUser ? "/dashboard" : "/"}>El Quds Admin Dashboard</Navbar.Brand>
                <Navbar.Toggle aria-controls="basic-navbar-nav"/>
                <Navbar.Collapse id="basic-navbar-nav">
                    <Nav className="me-auto">
                        {currentUser && permittedAdmin() && (
                            <>
                                <Nav.Link as={Link} to="/">Dashboard</Nav.Link>

                                {/* Products & Categories Menu */}
                                <NavDropdown title="Products" id="products-dropdown">
                                    <NavDropdown.Item as={Link} to="/categories">
                                        Categories
                                    </NavDropdown.Item>
                                    <NavDropdown.Item as={Link} to="/categories/create">
                                        Add Category
                                    </NavDropdown.Item>
                                    <NavDropdown.Divider/>
                                    <NavDropdown.Item as={Link} to="/products">
                                        Products
                                    </NavDropdown.Item>
                                    <NavDropdown.Item as={Link} to="/products/create">
                                        Add Product
                                    </NavDropdown.Item>
                                </NavDropdown>

                                {/* Warehouses Menu */}
                                <NavDropdown title="Warehouses" id="warehouses-dropdown">
                                    <NavDropdown.Item as={Link} to="/warehouses">
                                        Warehouses
                                    </NavDropdown.Item>
                                    <NavDropdown.Item as={Link} to="/warehouses/create">
                                        Add Warehouse
                                    </NavDropdown.Item>
                                    <NavDropdown.Divider/>
                                    <NavDropdown.Item as={Link} to="/inventory-counts">
                                        Inventory Counts
                                    </NavDropdown.Item>
                                    <NavDropdown.Item as={Link} to="/inventory-counts/create">
                                        New Inventory Count
                                    </NavDropdown.Item>
                                </NavDropdown>

                            </>
                        )}
                    </Nav>

                    <Nav>
                        {currentUser ? (
                            <NavDropdown
                                title={currentUser.name || 'User'}
                                id="user-dropdown"
                                align="end"
                            >
                                <NavDropdown.Item>
                                    Role: {currentUser.role}
                                </NavDropdown.Item>
                                <NavDropdown.Divider/>
                                <NavDropdown.Item onClick={handleLogout}>
                                    Logout
                                </NavDropdown.Item>
                            </NavDropdown>
                        ) : (
                            <>
                                <Nav.Link as={Link} to="/login">Login</Nav.Link>
                            </>
                        )}
                    </Nav>
                </Navbar.Collapse>
            </Container>
        </Navbar>
    );
};

export default Navigation;
